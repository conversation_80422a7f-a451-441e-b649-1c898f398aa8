using System;
using System.Data;
using System.Data.SQLite;
using System.IO;

class Program
{
    static void Main()
    {
        string dbPath = @"..\IPIS_Final\IPIS\IPIS\data\ipis.db";

        if (!File.Exists(dbPath))
        {
            Console.WriteLine($"Database file not found: {dbPath}");
            return;
        }

        string connectionString = $"Data Source={dbPath};Version=3;";

        try
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                Console.WriteLine("Successfully connected to main IPIS database");

                // Get table schema
                string schemaQuery = "SELECT name FROM sqlite_master WHERE type='table'";
                using (var command = new SQLiteCommand(schemaQuery, connection))
                using (var reader = command.ExecuteReader())
                {
                    Console.WriteLine("\nTables in database:");
                    while (reader.Read())
                    {
                        Console.WriteLine($"- {reader["name"]}");
                    }
                }

                // Check for common tables and their record counts
                string[] tablesToCheck = { "Users", "Station_Details", "Train_Data", "Online_Trains", "PA_Details" };

                foreach (string table in tablesToCheck)
                {
                    try
                    {
                        string countQuery = $"SELECT COUNT(*) FROM {table}";
                        using (var command = new SQLiteCommand(countQuery, connection))
                        {
                            var count = command.ExecuteScalar();
                            Console.WriteLine($"\n{table} records: {count}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"\nError accessing {table}: {ex.Message}");
                    }
                }

                // Check database integrity
                Console.WriteLine("\nChecking database integrity...");
                try
                {
                    string integrityQuery = "PRAGMA integrity_check";
                    using (var command = new SQLiteCommand(integrityQuery, connection))
                    {
                        var result = command.ExecuteScalar();
                        Console.WriteLine($"Database integrity: {result}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error checking database integrity: {ex.Message}");
                }

                // Check for foreign key violations
                Console.WriteLine("\nChecking foreign key constraints...");
                try
                {
                    string fkQuery = "PRAGMA foreign_key_check";
                    using (var command = new SQLiteCommand(fkQuery, connection))
                    using (var reader = command.ExecuteReader())
                    {
                        bool hasViolations = false;
                        while (reader.Read())
                        {
                            hasViolations = true;
                            Console.WriteLine($"Foreign key violation in table: {reader[0]}");
                        }
                        if (!hasViolations)
                        {
                            Console.WriteLine("No foreign key violations found.");
                        }
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error checking foreign keys: {ex.Message}");
                }

                // Get log level distribution
                string levelQuery = @"
                    SELECT Level, COUNT(*) as Count
                    FROM System_Logs
                    GROUP BY Level
                    ORDER BY Count DESC";

                using (var command = new SQLiteCommand(levelQuery, connection))
                using (var reader = command.ExecuteReader())
                {
                    Console.WriteLine("\n\nLog level distribution:");
                    Console.WriteLine(new string('-', 30));
                    while (reader.Read())
                    {
                        Console.WriteLine($"{reader["Level"]}: {reader["Count"]}");
                    }
                }

                // Get recent logs by category
                string categoryQuery = @"
                    SELECT Category, COUNT(*) as Count
                    FROM System_Logs
                    WHERE Timestamp >= datetime('now', '-7 days')
                    GROUP BY Category
                    ORDER BY Count DESC";

                using (var command = new SQLiteCommand(categoryQuery, connection))
                using (var reader = command.ExecuteReader())
                {
                    Console.WriteLine("\n\nLog categories (last 7 days):");
                    Console.WriteLine(new string('-', 30));
                    while (reader.Read())
                    {
                        Console.WriteLine($"{reader["Category"]}: {reader["Count"]}");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error accessing database: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }
}
