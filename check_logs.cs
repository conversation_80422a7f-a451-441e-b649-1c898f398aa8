using System;
using System.Data;
using System.Data.SQLite;
using System.IO;

class Program
{
    static void Main()
    {
        string dbPath = @"IPIS_Final\IPIS\IPIS\data\ipis_logs.db";
        
        if (!File.Exists(dbPath))
        {
            Console.WriteLine($"Database file not found: {dbPath}");
            return;
        }
        
        string connectionString = $"Data Source={dbPath};Version=3;";
        
        try
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                Console.WriteLine("Successfully connected to logs database");
                
                // Get table schema
                string schemaQuery = "SELECT name FROM sqlite_master WHERE type='table'";
                using (var command = new SQLiteCommand(schemaQuery, connection))
                using (var reader = command.ExecuteReader())
                {
                    Console.WriteLine("\nTables in database:");
                    while (reader.Read())
                    {
                        Console.WriteLine($"- {reader["name"]}");
                    }
                }
                
                // Check if System_Logs table exists and get count
                string countQuery = "SELECT COUNT(*) FROM System_Logs";
                using (var command = new SQLiteCommand(countQuery, connection))
                {
                    var count = command.ExecuteScalar();
                    Console.WriteLine($"\nTotal log entries: {count}");
                }
                
                // Get recent error and warning logs
                string errorQuery = @"
                    SELECT Timestamp, Level, Category, Message, Details, Exception 
                    FROM System_Logs 
                    WHERE Level IN ('Error', 'Warning') 
                    ORDER BY Timestamp DESC 
                    LIMIT 20";
                    
                using (var command = new SQLiteCommand(errorQuery, connection))
                using (var reader = command.ExecuteReader())
                {
                    Console.WriteLine("\nRecent errors and warnings:");
                    Console.WriteLine(new string('=', 80));
                    
                    int count = 0;
                    while (reader.Read())
                    {
                        count++;
                        Console.WriteLine($"\n{count}. [{reader["Timestamp"]}] {reader["Level"]} - {reader["Category"]}");
                        Console.WriteLine($"   Message: {reader["Message"]}");
                        
                        if (!reader.IsDBNull(reader.GetOrdinal("Details")) && !string.IsNullOrEmpty(reader["Details"].ToString()))
                        {
                            string details = reader["Details"].ToString();
                            if (details.Length > 200)
                                details = details.Substring(0, 200) + "...";
                            Console.WriteLine($"   Details: {details}");
                        }
                        
                        if (!reader.IsDBNull(reader.GetOrdinal("Exception")) && !string.IsNullOrEmpty(reader["Exception"].ToString()))
                        {
                            string exception = reader["Exception"].ToString();
                            if (exception.Length > 200)
                                exception = exception.Substring(0, 200) + "...";
                            Console.WriteLine($"   Exception: {exception}");
                        }
                    }
                    
                    if (count == 0)
                    {
                        Console.WriteLine("No errors or warnings found in recent logs.");
                    }
                }
                
                // Get log level distribution
                string levelQuery = @"
                    SELECT Level, COUNT(*) as Count 
                    FROM System_Logs 
                    GROUP BY Level 
                    ORDER BY Count DESC";
                    
                using (var command = new SQLiteCommand(levelQuery, connection))
                using (var reader = command.ExecuteReader())
                {
                    Console.WriteLine("\n\nLog level distribution:");
                    Console.WriteLine(new string('-', 30));
                    while (reader.Read())
                    {
                        Console.WriteLine($"{reader["Level"]}: {reader["Count"]}");
                    }
                }
                
                // Get recent logs by category
                string categoryQuery = @"
                    SELECT Category, COUNT(*) as Count 
                    FROM System_Logs 
                    WHERE Timestamp >= datetime('now', '-7 days')
                    GROUP BY Category 
                    ORDER BY Count DESC";
                    
                using (var command = new SQLiteCommand(categoryQuery, connection))
                using (var reader = command.ExecuteReader())
                {
                    Console.WriteLine("\n\nLog categories (last 7 days):");
                    Console.WriteLine(new string('-', 30));
                    while (reader.Read())
                    {
                        Console.WriteLine($"{reader["Category"]}: {reader["Count"]}");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error accessing database: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }
    }
}
